//@version=5
indicator("Advanced Time Technique (ATT) with Candle Numbers + Arrows", overlay=true, max_labels_count=500)

// HTF Box Settings
group_candle = 'HTF Box Settings'

// HTF Box 1
htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP1', group=group_candle)
htfUser1  = input.string('1 Hour', 'TF1', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP1', group=group_candle)
bullC1    = input.color(#26a69a, 'Bull1', inline='COL1', group=group_candle)
bearC1    = input.color(#ef5350, 'Bear1', inline='COL1', group=group_candle)

// HTF Box 2
htfCndl2  = input.bool(false, '2nd HTF Box', inline='TYP2', group=group_candle)
htfUser2  = input.string('4 Hours', 'TF2', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP2', group=group_candle)
bullC2    = input.color(#2196f3, 'Bull2', inline='COL2', group=group_candle)
bearC2    = input.color(#ff9800, 'Bear2', inline='COL2', group=group_candle)

// HTF Box 3
htfCndl3  = input.bool(false, '3rd HTF Box', inline='TYP3', group=group_candle)
htfUser3  = input.string('1 Day', 'TF3', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP3', group=group_candle)
bullC3    = input.color(#9c27b0, 'Bull3', inline='COL3', group=group_candle)
bearC3    = input.color(#e91e63, 'Bear3', inline='COL3', group=group_candle)

// Common Settings
group_common = 'Common Settings'
trans        = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=group_common)
lw           = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=group_common)
showNumbers  = input.bool(true, 'Show Candle Numbers', group=group_common)
numbersColor = input.color(color.white, 'Numbers Color', inline='NUM', group=group_common)
numbersSize  = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM', group=group_common)

// ATT Circle Settings
group_att = 'ATT Circle Settings'
showATT      = input.bool(true, 'Show ATT Circles', group=group_att)
attSize      = input.string(size.small, 'ATT Circle Size', options=[size.tiny, size.small, size.normal, size.large], inline='ATT_STYLE', group=group_att)
showATTNumbers = input.bool(true, 'Show ATT Numbers on Circles', inline='ATT_STYLE', group=group_att)
attColor1    = input.color(#8ee60ac4, 'HTF1 Color', inline='ATT_COL1', group=group_att)
attColor2    = input.color(#2196f3c4, 'HTF2 Color', inline='ATT_COL2', group=group_att)
attColor3    = input.color(#9c27b0c4, 'HTF3 Color', inline='ATT_COL3', group=group_att)

// The ATT candle numbers where arrow marks will be drawn
var att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 : _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)
    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 0
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

f_getTF(_htf) =>
    _htf == '3 Mins' ? '3' :_htf == '5 Mins' ? '5' :_htf == '10 Mins' ? '10' :_htf == '15 Mins' ? '15' :_htf == '30 Mins' ? '30' :_htf == '45 Mins' ? '45' :_htf == '1 Hour' ? '60' :_htf == '2 Hours' ? '120' :_htf == '3 Hours' ? '180' :_htf == '4 Hours' ? '240' :_htf == '1 Day' ? 'D' :_htf == '1 Week' ? 'W' :_htf == '1 Month' ? 'M' :_htf == '3 Months' ? '3M' :_htf == '6 Months' ? '6M' :_htf == '1 Year' ? '12M' : na

// Global label arrays for each HTF box
var label[] candleLabelsHTF1 = array.new_label()
var label[] candleLabelsHTF2 = array.new_label()
var label[] candleLabelsHTF3 = array.new_label()

// Function to check if current bar should show ATT circle
f_checkATTCondition(_show, _htf) =>
    result = false
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        result := array.includes(att_numbers, currentCandle)
    result

// Function to get ATT number for current bar
f_getATTNumber(_show, _htf) =>
    attNumber = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        if array.includes(att_numbers, currentCandle)
            attNumber := currentCandle
    attNumber



// Function to draw candle numbers independently
f_drawCandleNumbers(_show, _htf, _labelArr) =>
    if _show and showNumbers
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)
        labelPos = low - (high - low) * 0.5
        candleLabel = label.new(bar_index, labelPos, str.tostring(candleCount + 1), style=label.style_label_center, textcolor=numbersColor, size=numbersSize, yloc=yloc.price, color=na, textalign=text.align_center)
        array.push(_labelArr, candleLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 480
            label.delete(array.shift(_labelArr))

f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width, _labelArr, _htfNumber) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)

            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)

            // Cleanup labels to prevent overload
            if array.size(_labelArr) > 480
                label.delete(array.shift(_labelArr))

// ------------------------- Main Logic ------------------------- //

// Process HTF Box 1
htf1 = f_getTF(htfUser1)
supported1 = checkIf(timeframe.period, htf1)

// Process HTF Box 2
htf2 = f_getTF(htfUser2)
supported2 = checkIf(timeframe.period, htf2)

// Process HTF Box 3
htf3 = f_getTF(htfUser3)
supported3 = checkIf(timeframe.period, htf3)

if chart.is_standard
    // Draw HTF Box 1
    if supported1
        f_processCandles(htfCndl1, htf1, bullC1, bearC1, trans, lw, candleLabelsHTF1, 1)

    // Draw HTF Box 2
    if supported2
        f_processCandles(htfCndl2, htf2, bullC2, bearC2, trans, lw, candleLabelsHTF2, 2)

    // Draw HTF Box 3
    if supported3
        f_processCandles(htfCndl3, htf3, bullC3, bearC3, trans, lw, candleLabelsHTF3, 3)

// Calculate ATT conditions for each HTF timeframe
attResult1 = supported1 ? f_checkATTCondition(htfCndl1, htf1) : [false, 0]
attCondition1 = array.get(attResult1, 0)
attNumber1 = array.get(attResult1, 1)

attResult2 = supported2 ? f_checkATTCondition(htfCndl2, htf2) : [false, 0]
attCondition2 = array.get(attResult2, 0)
attNumber2 = array.get(attResult2, 1)

attResult3 = supported3 ? f_checkATTCondition(htfCndl3, htf3) : [false, 0]
attCondition3 = array.get(attResult3, 0)
attNumber3 = array.get(attResult3, 1)

// Plot ATT circles using plotshape (must be in global scope for historical display)
// HTF1 - Different plotshape for each ATT number to show the number on the circle
plotshape(showATT and attCondition1 and attNumber1 == 3, title="ATT HTF1-3", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "3" : "")
plotshape(showATT and attCondition1 and attNumber1 == 11, title="ATT HTF1-11", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "11" : "")
plotshape(showATT and attCondition1 and attNumber1 == 17, title="ATT HTF1-17", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "17" : "")
plotshape(showATT and attCondition1 and attNumber1 == 29, title="ATT HTF1-29", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "29" : "")
plotshape(showATT and attCondition1 and attNumber1 == 41, title="ATT HTF1-41", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "41" : "")
plotshape(showATT and attCondition1 and attNumber1 == 47, title="ATT HTF1-47", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "47" : "")
plotshape(showATT and attCondition1 and attNumber1 == 53, title="ATT HTF1-53", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "53" : "")
plotshape(showATT and attCondition1 and attNumber1 == 59, title="ATT HTF1-59", style=shape.circle, location=location.abovebar, color=attColor1, size=attSize, text=showATTNumbers ? "59" : "")

// HTF2 - Below bars to avoid overlap
plotshape(showATT and attCondition2 and attNumber2 == 3, title="ATT HTF2-3", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "3" : "")
plotshape(showATT and attCondition2 and attNumber2 == 11, title="ATT HTF2-11", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "11" : "")
plotshape(showATT and attCondition2 and attNumber2 == 17, title="ATT HTF2-17", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "17" : "")
plotshape(showATT and attCondition2 and attNumber2 == 29, title="ATT HTF2-29", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "29" : "")
plotshape(showATT and attCondition2 and attNumber2 == 41, title="ATT HTF2-41", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "41" : "")
plotshape(showATT and attCondition2 and attNumber2 == 47, title="ATT HTF2-47", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "47" : "")
plotshape(showATT and attCondition2 and attNumber2 == 53, title="ATT HTF2-53", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "53" : "")
plotshape(showATT and attCondition2 and attNumber2 == 59, title="ATT HTF2-59", style=shape.circle, location=location.belowbar, color=attColor2, size=attSize, text=showATTNumbers ? "59" : "")

// HTF3 - Triangles above bars to distinguish from HTF1
plotshape(showATT and attCondition3 and attNumber3 == 3, title="ATT HTF3-3", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "3" : "")
plotshape(showATT and attCondition3 and attNumber3 == 11, title="ATT HTF3-11", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "11" : "")
plotshape(showATT and attCondition3 and attNumber3 == 17, title="ATT HTF3-17", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "17" : "")
plotshape(showATT and attCondition3 and attNumber3 == 29, title="ATT HTF3-29", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "29" : "")
plotshape(showATT and attCondition3 and attNumber3 == 41, title="ATT HTF3-41", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "41" : "")
plotshape(showATT and attCondition3 and attNumber3 == 47, title="ATT HTF3-47", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "47" : "")
plotshape(showATT and attCondition3 and attNumber3 == 53, title="ATT HTF3-53", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "53" : "")
plotshape(showATT and attCondition3 and attNumber3 == 59, title="ATT HTF3-59", style=shape.triangleup, location=location.abovebar, color=attColor3, size=attSize, text=showATTNumbers ? "59" : "")



// Draw candle numbers for the primary HTF timeframe (HTF1 by default, or first enabled one)
if supported1 and htfCndl1
    f_drawCandleNumbers(true, htf1, candleLabelsHTF1)
else if supported2 and htfCndl2
    f_drawCandleNumbers(true, htf2, candleLabelsHTF2)
else if supported3 and htfCndl3
    f_drawCandleNumbers(true, htf3, candleLabelsHTF3)